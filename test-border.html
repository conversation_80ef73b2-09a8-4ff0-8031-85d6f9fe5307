<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Border Test</title>
    <style>
        :root {
            --left-menu-border-color: #ff0000; /* 红色测试边框 */
            --left-menu-bg-color: #001529;
        }
        
        .test-container {
            width: 300px;
            height: 400px;
            background-color: var(--left-menu-bg-color);
            position: relative;
            margin: 50px;
        }
        
        .layout-border__right:after {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 3px; /* 加宽边框便于测试 */
            height: 100%;
            background-color: var(--left-menu-border-color);
            z-index: 3001;
            pointer-events: none;
        }
        
        .test-light {
            --left-menu-border-color: #e4e7ed;
            --left-menu-bg-color: #ffffff;
        }
    </style>
</head>
<body>
    <h1>边框测试</h1>
    
    <h2>深色主题 (默认)</h2>
    <div class="test-container layout-border__right">
        <p style="color: white; padding: 20px;">深色背景，白色半透明边框</p>
    </div>
    
    <h2>浅色主题</h2>
    <div class="test-container layout-border__right test-light">
        <p style="color: black; padding: 20px;">浅色背景，灰色边框</p>
    </div>
    
    <h2>测试说明</h2>
    <p>如果边框可见，说明CSS规则正确。如果看不到边框，可能需要调整颜色或z-index。</p>
</body>
</html>
